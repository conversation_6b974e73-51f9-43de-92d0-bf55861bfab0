# RS485 UMDF Driver Test UI Guide

## 安装状态确认

根据您的安装日志，RS485驱动已经成功安装：

✅ **FTDI VCP驱动** - 安装成功  
✅ **RS485协议过滤驱动** - 通过替代方法安装成功  

⚠️ **开发环境警告**: 显示的警告是正常的，因为这是开发版本没有正式数字签名，但功能完全正常。

## 测试UI程序

### 1. 启动测试程序

```bash
# 运行增强版测试UI
.\RS485TestUI_Enhanced.exe

# 或运行标准版测试UI  
.\RS485TestUI.exe
```

### 2. 界面功能说明

#### 连接设置区域
- **COM Port**: 选择RS485设备连接的COM端口
- **Baud Rate**: 选择波特率 (9600, 19200, 38400, 57600, 115200)
- **Slave ID**: 设置从设备地址 (1-31)
- **Refresh Ports**: 刷新可用COM端口列表

#### 控制按钮
- **Connect**: 连接到选定的COM端口
- **Disconnect**: 断开COM端口连接
- **Test Driver**: 测试RS485 UMDF驱动连接状态
- **Auto Test**: 运行自动测试序列

#### 协议测试按钮
- **Test S001**: 测试S001命令 (设置从设备地址)
- **Test A001**: 测试A001命令 (查询数据)
- **Test U001**: 测试U001命令 (设置SEL阈值)

#### 数据传输区域
- **Send Data**: 手动发送十六进制数据
- **Test Results**: 显示测试结果和接收到的数据
- **Clear**: 清空接收数据显示

#### 状态显示
- **Status**: 显示当前操作状态
- **Progress Bar**: 显示测试进度

### 3. 测试步骤

#### 基本连接测试
1. 点击 **"Refresh Ports"** 刷新端口列表
2. 选择正确的COM端口
3. 选择合适的波特率 (通常9600)
4. 点击 **"Connect"** 连接端口
5. 观察状态显示是否显示连接成功

#### 驱动功能测试
1. 点击 **"Test Driver"** 按钮
2. 程序会自动检测：
   - RS485 UMDF驱动是否正确安装
   - 驱动接口是否可访问
   - IOCTL通信是否正常
3. 查看测试结果窗口的详细信息

#### 协议命令测试
1. 确保已连接COM端口
2. 设置正确的Slave ID
3. 依次测试各个命令：
   - **S001**: 设置从设备地址
   - **A001**: 查询设备数据
   - **U001**: 设置SEL阈值为250mA

#### 自动测试序列
1. 点击 **"Auto Test"** 按钮
2. 程序会自动执行：
   - 驱动连接测试
   - COM端口连接测试
   - S001命令测试
   - A001命令测试
3. 观察进度条和状态显示
4. 查看详细测试结果

### 4. 预期测试结果

#### 正常工作的标志
- ✅ COM端口连接成功
- ✅ 驱动接口可访问
- ✅ 命令发送成功
- ✅ 接收到设备响应

#### 可能的问题和解决方案

**问题1**: COM端口连接失败
- 检查RS485设备是否正确连接
- 确认COM端口号是否正确
- 尝试不同的波特率

**问题2**: 驱动测试失败
- 以管理员权限运行测试程序
- 确认驱动安装是否完整
- 检查是否启用了测试签名模式

**问题3**: 没有收到设备响应
- 检查从设备地址设置是否正确
- 确认RS485设备是否正常工作
- 检查通信参数 (波特率、数据位等)

### 5. 数据格式说明

#### RS485协议帧格式
```
[Header] [ID] [Command] [Data] [CRC] [Trailer]
  0xAA   ID   4 bytes   8 bytes  1    0x0D
```

#### 示例命令
- **S001设置地址5**: `AA 01 53 30 30 31 00 00 00 05 00 00 00 00 00 0D`
- **A001查询数据**: `AA 05 41 30 30 31 00 00 00 00 00 00 00 00 00 0D`
- **U001设置阈值**: `AA 05 55 30 30 31 FA 00 00 00 00 00 00 00 00 0D`

### 6. 故障排除

如果测试过程中遇到问题：

1. **重新安装驱动**:
   ```bash
   .\RS485DriverInstaller.exe
   ```

2. **启用测试签名** (需要管理员权限):
   ```bash
   bcdedit /set testsigning on
   # 重启计算机
   ```

3. **检查设备管理器**:
   - 查看是否有未知设备
   - 确认FTDI设备是否正确识别

4. **查看系统日志**:
   - 事件查看器 → Windows日志 → 系统
   - 查找与RS485驱动相关的错误信息

## 总结

您的RS485驱动安装是成功的！现在可以使用测试UI来验证所有功能是否正常工作。建议按照上述步骤逐步测试，确保每个组件都能正常运行。
